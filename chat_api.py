import contextlib
import logging
import asyncio
import os
import time
from typing import List, Dict, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException,Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import dotenv

import processing
from prompt_template import prompt_template
from llm_model_config import OpenAIConfig
from utils import SessionManager, StreamingChatbot
from utils.openai_proxy import ChatCompletionRequest, ResponseTransformer, proxy_service


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 注意：StreamingChatbot 和 SessionManager 现在在 utils 模块中

# 初始化FastAPI应用
app = FastAPI(
    title="智能问答API",
    description="基于大模型的智能问答系统，支持上下文记录",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化模型配置
openai_config = OpenAIConfig()

def load_config():
    """加载配置，优先使用外部挂载的 .env 文件"""
    # 1. 优先尝试加载挂载卷中的 .env 文件
    external_env_path = '/app/config/.env'

    # 2. 如果挂载卷中有 .env，使用外部的
    if os.path.exists(external_env_path):
        print("使用外部配置文件:", external_env_path)
        # 加载环境变量
        dotenv.load_dotenv(external_env_path)
    # 3. 否则使用项目内的 .env（开发环境或回退方案）
    elif os.path.exists('.env'):
        print("使用内部配置文件: .env")
        dotenv.load_dotenv('.env')
    else:
        print("未找到配置文件，使用环境变量")

load_config()
# 创建流式对话机器人实例
chatbot = StreamingChatbot(
    api_key=openai_config.api_key,
    base_url=openai_config.api_base,
    model=openai_config.model_name,
    temperature=openai_config.temperature
)

# 初始化会话管理器
session_manager = SessionManager()

# 定期清理任务
async def cleanup_task():
    """定期清理过期会话"""
    while True:
        try:
            await asyncio.sleep(3600)  # 每小时清理一次
            cleaned_count = session_manager.cleanup_expired_sessions()
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期会话")
        except Exception as e:
            logger.error(f"清理任务错误: {e}")

# 应用启动事件处理器
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    # 启动清理任务
    asyncio.create_task(cleanup_task())
    logger.info("应用启动完成，清理任务已启动")

# Pydantic模型
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        # 输入验证
        if not self.message or not self.message.strip():
            raise ValueError("消息不能为空")
        self.message = self.message.strip()

class ChatAnalysisMessage(BaseModel):
    message: dict
    session_id: Optional[str] = None
    date: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        # 输入验证
        if not self.message :
            raise ValueError("消息不能为空")


class ChatResponse(BaseModel):
    response: str
    session_id: str
    timestamp: str

class StreamResponse(BaseModel):
    type: str  # "token", "end", "error"
    content: str
    session_id: str
    timestamp: str
    id: Optional[str] = None  # SSE事件ID
    event: Optional[str] = None  # SSE事件类型

class SessionInfo(BaseModel):
    session_id: str
    created_at: str
    last_activity: str
    message_count: int

class SessionListResponse(BaseModel):
    sessions: List[SessionInfo]

# 辅助函数：处理会话管理和聊天响应
async def _handle_chat_request(session_id: Optional[str], message: str) -> ChatResponse:
    """处理聊天请求的公共逻辑"""
    try:
        # 如果没有提供session_id，创建新会话
        if not session_id:
            session_id = session_manager.create_session()
        elif not session_manager.get_session(session_id):
            # 如果提供的session_id不存在，创建新会话
            session_id = session_manager.create_session(session_id)

        # 更新会话活动
        session_manager.update_session_activity(session_id)

        # 使用聊天机器人进行对话
        ai_response = chatbot.chat(message, session_id)

        return ChatResponse(
            response=ai_response,
            session_id=session_id,
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        logger.error(f"聊天处理错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理消息时发生错误: {str(e)}")

# API端点
@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatMessage):
    """发送消息并获取AI回复"""
    return await _handle_chat_request(request.session_id, request.message)

@app.post("/api/chat/analysis", response_model=ChatResponse)
async def chat_analysis(request: ChatAnalysisMessage):
    """发送消息并获取AI回复（带分析模板）"""
    # 使用模板处理消息
    result = processing.convert_api_to_llm_input(request.message)
    print(result)
    processed_message = prompt_template.invoke({"INPUT_DATA":result}).text
    return await _handle_chat_request(request.session_id, processed_message)

# 辅助函数：处理流式聊天请求
async def _handle_stream_chat_request(session_id: Optional[str], message: str) -> StreamingResponse:
    """处理流式聊天请求的公共逻辑"""
    async def generate_stream():
        try:
            # 如果没有提供session_id，创建新会话
            if not session_id:
                current_session_id = session_manager.create_session()
            elif not session_manager.get_session(session_id):
                # 如果提供的session_id不存在，创建新会话
                current_session_id = session_manager.create_session(session_id)
            else:
                current_session_id = session_id

            # 更新会话活动
            session_manager.update_session_activity(current_session_id)

            # 使用简化的流式对话机器人
            token_count = 0

            # 直接使用流式对话机器人的stream_chat方法
            async for chunk in chatbot.stream_chat(message, current_session_id):
                # 发送每个token
                token_count += 1
                stream_data = StreamResponse(
                    type="token",
                    content=chunk,
                    session_id=current_session_id,
                    timestamp=datetime.now().isoformat(),
                    id=str(token_count),
                    event="message"
                )
                # yield f"id: {stream_data.id}\nevent: {stream_data.event}\ndata: {stream_data.json()}\n\n"
                data = f"id: {stream_data.id}\nevent: {stream_data.event}\ndata: {stream_data.json()}\n\n"
                yield data
                # 添加立即刷新
                await asyncio.sleep(0.001)
            # 发送结束信号
            end_data = StreamResponse(
                type="end",
                content="",
                session_id=current_session_id,
                timestamp=datetime.now().isoformat(),
                id=str(token_count + 1),
                event="end"
            )
            yield f"id: {end_data.id}\nevent: {end_data.event}\ndata: {end_data.json()}\n\n"

        except Exception as e:
            logger.error(f"流式聊天处理错误: {str(e)}")
            error_data = StreamResponse(
                type="error",
                content=f"服务器错误: {str(e)}",
                session_id=session_id or "unknown",
                timestamp=datetime.now().isoformat(),
                id="error",
                event="error"
            )
            yield f"id: {error_data.id}\nevent: {error_data.event}\ndata: {error_data.json()}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )

@app.post("/api/chat/stream")
async def chat_stream(request: ChatMessage):
    """流式发送消息并获取AI回复"""
    return await _handle_stream_chat_request(request.session_id, request.message)

@app.post("/api/chat/stream/analysis")
async def chat_stream_analysis(request: ChatAnalysisMessage):
    """流式发送消息并获取AI回复（带分析模板）"""
    print("=== 开始性能分析 ===", flush=True)
    total_start = time.time()

    # 分析请求数据
    print(f"请求session_id: {request.session_id}", flush=True)
    print(f"原始消息长度: {len(str(request.message))}", flush=True)

    # 步骤1: 请求数据接口
    try:
        print(f"开始请求数据接口...", flush=True)
        response = request.post('http://localhost:8000/api/chat/analysis', json={"message": request.message})
        print(f"✅ 请求数据接口成功", flush=True)
    except Exception as e:
        print(f"❌ 请求数据接口失败: {e}", flush=True)
        raise
    # 步骤2: 模板处理
    step1_start = time.time()
    try:
        result = processing.convert_api_to_llm_input(request.message)
        step1_time = time.time() - step1_start
        print(f"✅ 模板处理完成 - 耗时: {step1_time:.3f}秒", flush=True)
        print(f"   处理结果类型: {type(result)}, 长度: {len(str(result))}", flush=True)
    except Exception as e:
        print(f"❌ 模板处理失败: {e}", flush=True)
        raise

    # 步骤2: 提示词调用
    step2_start = time.time()
    try:
        processed_message = prompt_template.invoke({"INPUT_DATA": result}).text
        step2_time = time.time() - step2_start
        print(f"✅ 提示词调用完成 - 耗时: {step2_time:.3f}秒", flush=True)
        print(f"   最终消息长度: {len(processed_message)}", flush=True)
    except Exception as e:
        print(f"❌ 提示词调用失败: {e}", flush=True)
        raise

    debug_log(processed_message)
    # 步骤3: 流式处理
    step3_start = time.time()
    try:
        print(f"开始流式处理...", flush=True)
        response = await _handle_stream_chat_request(request.session_id, processed_message)
        step3_time = time.time() - step3_start
        print(f"✅ 流式处理初始化完成 - 耗时: {step3_time:.3f}秒", flush=True)
    except Exception as e:
        print(f"❌ 流式处理失败: {e}", flush=True)
        raise

    total_time = time.time() - total_start
    print(f"=== 总执行时间: {total_time:.3f}秒 ===", flush=True)
    print(f"接口准备返回流式响应", flush=True)

    return response

def debug_log(message):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    with open(os.path.join(current_dir, "requestM.txt"), "w") as f:
        f.write(f"{message}\n")

@app.get("/sessions", response_model=SessionListResponse)
async def list_sessions():
    """获取所有会话列表"""
    sessions = session_manager.list_sessions()
    return SessionListResponse(sessions=sessions)

@app.get("/sessions/{session_id}", response_model=SessionInfo)
async def get_session(session_id: str):
    """获取特定会话信息"""
    session = session_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")

    return SessionInfo(
        session_id=session_id,
        created_at=session["created_at"].isoformat(),
        last_activity=session["last_activity"].isoformat(),
        message_count=session["message_count"]
    )

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """删除会话"""
    if not session_manager.get_session(session_id):
        raise HTTPException(status_code=404, detail="会话不存在")

    session_manager.delete_session(session_id)
    return {"message": "会话已删除"}

@app.post("/sessions", response_model=SessionInfo)
async def create_session():
    """创建新会话"""
    session_id = session_manager.create_session()
    session = session_manager.get_session(session_id)

    return SessionInfo(
        session_id=session_id,
        created_at=session["created_at"].isoformat(),
        last_activity=session["last_activity"].isoformat(),
        message_count=session["message_count"]
    )

@app.post("/sessions/cleanup")
async def cleanup_sessions():
    """手动清理过期会话"""
    try:
        cleaned_count = session_manager.cleanup_expired_sessions()
        return {
            "message": f"清理了 {cleaned_count} 个过期会话",
            "cleaned_count": cleaned_count,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"手动清理会话错误: {e}")
        raise HTTPException(status_code=500, detail=f"清理会话时发生错误: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "model": openai_config.model_name,
        "active_sessions": session_manager.get_session_count(),
        "chatbot_ready": chatbot is not None
    }

@app.post("/v1/chat/completions")
async def chat_completions(
        request: ChatCompletionRequest
):
    logger.info(f"收到请求: {request}")
    api_key = openai_config.api_key
    if request.stream:
        return StreamingResponse(
            proxy_service.forward_stream_request(request, api_key),
            media_type="text/event-stream"
        )
    else:
        llm_response = await proxy_service.forward_request(request, api_key)
        return ResponseTransformer.transform_response(llm_response, request.model)

# @app.post("/internal/v1/chat/completions")
# async def internal_chat_completions(
#         request: ChatCompletionRequest
# ):
#     """内部接口，直接调用大模型服务，避免循环调用"""
#     logger.info(f"内部接口收到请求: {request}")
#     api_key = openai_config.api_key
#     if request.stream:
#         return StreamingResponse(
#             proxy_service.forward_stream_request(request, api_key),
#             media_type="text/event-stream"
#         )
#     else:
#         llm_response = await proxy_service.forward_request(request, api_key)
#         return ResponseTransformer.transform_response(llm_response, request.model)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
