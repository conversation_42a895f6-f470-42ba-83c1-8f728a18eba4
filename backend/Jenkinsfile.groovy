#!groovy

pipeline {
    agent any
    options {
        // 限制保存构建的数量
        buildDiscarder(logRotator(numToKeepStr: '5'))
        // 禁止单个WorkSpace内发生并行构建
        disableConcurrentBuilds()
    }

    // 全局变量
    environment {
        //邮件通知，多人使用逗号隔开
        MAIL_RECIPIENTS = '<EMAIL>'
    }

    stages {
        stage('Define Environment') {
            steps {
                // 定义项目配置
                script {
                    // 定义代码仓库信息
                    GIT_REPO = "http://git.tsintergy.com:8070/eo/proj/xjjy-llm/xjjy_llm_backend.git"
                    // 代码仓库分支
                    GIT_BRANCH = "proj/xinjiang/feature/singe"
                    // Jenkins凭据ID
                    CREDENTIALS_ID = "0da7711f-8c84-4b76-a23f-071e0a6fa595"
                    // 镜像标签，格式为镜像名称+冒号+版本号
                    IMAGE_TAG = "lf-xinjiang-jy-backend:1.0.0"
                }
            }
        }

        stage('Checkout') {
            steps {
                ansiColor('xterm') {
                    git url: "${GIT_REPO}", branch: "${GIT_BRANCH}", credentialsId: "${CREDENTIALS_ID}"
                }
            }
        }

//        stage('Build') {
//            steps {
//                ansiColor('xterm') {
//                    dir("${WORKSPACE}/") {
//                        // 设置Maven使用JDK8进行编译
//                        sh """
//                            # 设置JDK8环境
//                            export JAVA_HOME=/home/<USER>/
//                            export PATH=\$JAVA_HOME/bin:\$PATH
//                            export MAVEN_OPTS="-Xmx1024m -Xms512m"
//
//                            # 验证Java版本
//                            echo "当前Java版本："
//                            java -version
//
//                            echo "当前Maven版本："
//                            mvn -version
//
//                            # 执行Maven构建
//                            mvn clean package -U -Dmaven.javadoc.skip=true -DskipTests -Ppackage-springboot
//                        """
//                    }
//                }
//            }
//        }

        stage('Install') {
            steps {
                ansiColor('xterm') {
                    sh "docker login -udev -pQinghua123@ registry.tsintergy.com"
//                    sh "docker pull registry.tsintergy.com/tsintergy-public/openjdk:8"
                    sh "docker build . -t registry.tsintergy.com/tsintergy/${IMAGE_TAG} -f ./Dockerfile"
                    sh "docker push registry.tsintergy.com/tsintergy/${IMAGE_TAG}"
                    sh "docker rmi registry.tsintergy.com/tsintergy/${IMAGE_TAG}"
//                    sh "curl -d '{\"secret_key\":\"AGL8Ut59\"}' -H \"Content-type: application/json\" -X POST http://*************:7070/console/custom/deploy/453c9aa1e835e987cc85d1f6f31078c0"
                }
            }
        }
    }

    post() {
        failure {
            mail to: "${MAIL_RECIPIENTS}",
                    subject: "Build failed in Jenkins: ${currentBuild.fullDisplayName}",
                    body: "${currentBuild.result},see <${env.BUILD_URL}>"
        }
    }
}
