import json


# 加载配置文件
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)


def convert_api_to_llm_input(api_response):
    """
    将API响应转换为LLM输入格式

    输入: api_response - API返回的完整数据
          config - 配置参数
    输出: LLM输入JSON
    """
    result = {}

    # 提取月份，处理空数组情况
    import datetime

    # 尝试从fdczcqhyjypl获取时间
    if api_response["fdczcqhyjypl"] and len(api_response["fdczcqhyjypl"]) > 0:
        time_title = api_response["fdczcqhyjypl"][0]["timeTitle"]
    # 如果为空，尝试从zsbphzjbdl获取时间
    elif api_response["zsbphzjbdl"] and len(api_response["zsbphzjbdl"]) > 0:
        time_title = api_response["zsbphzjbdl"][0]["timeTitle"]
    # 如果都为空，使用当前时间
    else:
        time_title = datetime.datetime.now().strftime("%Y-%m-%d")

    result["month"] = convert_to_chinese_month(time_title)  # "2025年10月"

    # 指标1: 计算波动率
    result["volatility"] = calculate_volatility(api_response["zsbphzjbdl"])

    # 指标2: 发电侧交易频率异常
    result["gen_freq_anomalies"] = filter_frequency_anomalies(
        api_response["fdczcqhyjypl"]
    )

    # 指标3: 用电侧交易频率异常
    result["load_freq_anomalies"] = filter_frequency_anomalies(
        api_response["ydczcqhyjypl"]
    )

    # 指标4: 用电侧中长期vs日前
    result["load_longterm_dayahead"] = process_deviation_indicator(
        api_response["ydczqrqsbdl"],
        api_response["ydczcqjhydl"],
        config["deviation_configs"]["load_longterm_dayahead"]["medium_threshold"],
        config["deviation_configs"]["load_longterm_dayahead"]["high_threshold"],
        config["deviation_configs"]["load_longterm_dayahead"]["max_high_count"],
        config["deviation_configs"]["load_longterm_dayahead"]["max_medium_count"]
    )

    # 指标5: 用电侧实际vs日前
    result["load_actual_dayahead"] = process_deviation_indicator(
        api_response["ydcsjydl"],
        api_response["ydczqrqsbdl"],
        config["deviation_configs"]["load_actual_dayahead"]["medium_threshold"],
        config["deviation_configs"]["load_actual_dayahead"]["high_threshold"],
        config["deviation_configs"]["load_actual_dayahead"]["max_high_count"],
        config["deviation_configs"]["load_actual_dayahead"]["max_medium_count"]
    )

    # 指标6: 发电侧中长期vs日前
    result["gen_longterm_dayahead"] = process_deviation_indicator(
        api_response["fdcrqjydl"],
        api_response["fdczcqjhydl"],
        config["deviation_configs"]["gen_longterm_dayahead"]["medium_threshold"],
        config["deviation_configs"]["gen_longterm_dayahead"]["high_threshold"],
        config["deviation_configs"]["gen_longterm_dayahead"]["max_high_count"],
        config["deviation_configs"]["gen_longterm_dayahead"]["max_medium_count"]
    )

    # 指标7: 发电侧实际vs日前
    result["gen_actual_dayahead"] = process_deviation_indicator(
        api_response["fdcsjfdl"],
        api_response["fdcrqjydl"],
        config["deviation_configs"]["gen_actual_dayahead"]["medium_threshold"],
        config["deviation_configs"]["gen_actual_dayahead"]["high_threshold"],
        config["deviation_configs"]["gen_actual_dayahead"]["max_high_count"],
        config["deviation_configs"]["gen_actual_dayahead"]["max_medium_count"]
    )

    # 统计总主体数
    result["total_gen_entities"] = count_unique_entities(api_response["fdcrqjydl"])
    result["total_load_entities"] = count_unique_entities(api_response["ydczqrqsbdl"])

    # 新增指标处理
    # 指标8 市场偏差电费净额占比
    if "scpcdfjezb" in api_response and api_response["scpcdfjezb"]:
        result["scpcdfjezb"] = {
            "_comment_scpcdfjezb": "市场偏差电费净额占比 （每月一个值）",
            "timeTitle": api_response["scpcdfjezb"]["timeTitle"],
            "value": float(api_response["scpcdfjezb"]["value"])
        }

    # 指标9 阻塞费用集中度-天
    if "zsfyjzdt" in api_response and api_response["zsfyjzdt"]:
        result["zsfyjzdt"] = process_zsfyjzdt_new(
            api_response["zsfyjzdt"],
            config["concentration_configs"]["zsfyjzdt"]["top_units_count"],
            config["concentration_configs"]["zsfyjzdt"]["top_days_count"]
        )

    # 指标10 阻塞费用集中度-月
    if "zsfyjzdy" in api_response and api_response["zsfyjzdy"]:
        result["zsfyjzdy"] = process_zsfyjzdy_new(
            api_response["zsfyjzdy"],
            config["concentration_configs"]["zsfyjzdy"]["top_units_count"]
        )

    # 指标11 实时-日前价格离散指数-总-天
    if "ssrqjglszszt" in api_response and api_response["ssrqjglszszt"]:
        result["ssrqjglszszt"] = process_ssrqjglszszt_new(
            api_response["ssrqjglszszt"],
            config["concentration_configs"]["ssrqjglszszt"]["top_days_count"]
        )

    # 指标12 实时-日前价格离散指数-总-月
    if "ssrqjglszszy" in api_response and api_response["ssrqjglszszy"]:
        result["ssrqjglszszy"] = {
            "_comment_ssrqjglszszy": "实时-日前价格离散指数-总-月（每月一个值）",
            "timeTitle": api_response["ssrqjglszszy"][0]["timeTitle"] if isinstance(api_response["ssrqjglszszy"], list) else api_response["ssrqjglszszy"]["timeTitle"],
            "value": float(api_response["ssrqjglszszy"][0]["value"]) if isinstance(api_response["ssrqjglszszy"], list) else float(api_response["ssrqjglszszy"]["value"])
        }

    # 指标13 实时-日前价格离散指数-地区-天
    if "ssrqjglszsdqt" in api_response and api_response["ssrqjglszsdqt"]:
        result["ssrqjglszsdqt"] = process_ssrqjglszsdqt_new(
            api_response["ssrqjglszsdqt"]
        )

    #指标14 实时-日前价格离散指数-区域-月
    if "ssrqjglszsdqy" in api_response and api_response["ssrqjglszsdqy"]:
        result["ssrqjglszsdqy"] = process_ssrqjglszsdqy_new(
            api_response["ssrqjglszsdqy"]
        )

    # 指标15 省间双轨制影响度
    if "sjsgzyxd" in api_response and api_response["sjsgzyxd"]:
        result["sjsgzyxd"] = {
            "_comment_sjsgzyxd": "省间双轨制影响度（一个月一个值）",
            "timeTitle": api_response["sjsgzyxd"]["timeTitle"],
            "value": float(api_response["sjsgzyxd"]["value"])
        }


    # 转换所有numpy类型为Python原生类型
    result = convert_numpy_types(result)

    return result


def process_deviation_indicator(numerator_data, denominator_data,
                                medium_threshold, high_threshold,
                                max_high_count=50, max_medium_count=100):
    """
    处理一个偏差率指标的完整流程

    示例：处理指标4（用电侧中长期vs日前）
    numerator_data = ydczqrqsbdl     # 日前申报
    denominator_data = ydczcqjhydl   # 中长期合约
    medium_threshold = 8.0
    high_threshold = 15.0
    """
    # 步骤1: 计算所有偏差率
    entity_deviations = calculate_all_deviations(numerator_data, denominator_data)

    # 步骤2: 计算P95代表性偏差率
    representative = calculate_representative_deviation(entity_deviations)

    # 步骤3: 分类并限制数量
    result = classify_and_limit_risks(
        representative,
        medium_threshold,
        high_threshold,
        max_high_count,
        max_medium_count
    )

    return result

def calculate_all_deviations(numerator_data, denominator_data):
    """
    计算整月所有主体所有时点的偏差率

    输入:
        numerator_data = [
            {"timeTitle": "2025-10-01", "id": "use001", "name": "用户1",
             "tvValue": [10.1, 10.2, ..., 12.4]},  # 24小时
            ...整月数据
        ]
        denominator_data = [类似结构]

    输出:
        {
            "use001": [2.3, 5.1, ..., 8.9],  # 该主体整月所有时点的偏差率
            "use002": [...],
            ...
        }
    """
    # 按主体组织数据
    entity_deviations = {}

    # 构建分母数据的快速查找索引
    denominator_index = {}
    for item in denominator_data:
        key = (item["id"], item["timeTitle"])
        denominator_index[key] = item["tvValue"]

    # 遍历分子数据
    for num_item in numerator_data:
        entity_id = num_item["id"]
        entity_name = num_item["name"]
        date = num_item["timeTitle"]
        num_values = num_item["tvValue"]

        # 获取对应的分母数据
        key = (entity_id, date)
        if key not in denominator_index:
            continue  # 缺少分母数据，跳过

        denom_values = denominator_index[key]

        # 初始化该主体的偏差率列表
        if entity_id not in entity_deviations:
            entity_deviations[entity_id] = {
                "name": entity_name,
                "deviations": []
            }

        # 确保entity_deviations[entity_id]是字典类型
        if not isinstance(entity_deviations[entity_id], dict):
            entity_deviations[entity_id] = {
                "name": entity_name,
                "deviations": []
            }

        # 确保deviations键存在
        if "deviations" not in entity_deviations[entity_id]:
            entity_deviations[entity_id]["deviations"] = []

        # 计算小时偏差率，使用实际数据长度而非固定24小时
        hour_count = min(len(num_values), len(denom_values))
        for hour in range(hour_count):
            numerator = num_values[hour]
            denominator = denom_values[hour]

            # 分母不为0才计算
            if denominator != 0:
                deviation = abs((numerator - denominator) / denominator * 100)
                entity_deviations[entity_id]["deviations"].append(deviation)

    return entity_deviations


def calculate_volatility(zsbphzjbdl):
    """
    计算阻塞不平衡资金波动率

    输入: zsbphzjbdl = [
        {"timeTitle": "2025-10", "value": 100.5},
        {"timeTitle": "2025-09", "value": 98.3},
        {"timeTitle": "2025-08", "value": None},  # 可能包含None值
        ...共6个月
    ]
    输出: volatility = 2.3  (float)
    """
    # 提取6个月的value值，过滤掉None值
    values = [item["value"] for item in zsbphzjbdl if item["value"] is not None]

    # 如果没有有效值，返回0
    if not values:
        return 0.0

    # 计算标准差
    import math
    mean = sum(values) / len(values)
    variance = sum((x - mean) ** 2 for x in values) / len(values)
    std_dev = math.sqrt(variance)

    return round(std_dev, 2)  # 保留2位小数

def calculate_representative_deviation(entity_deviations):
    """
    为每个主体计算月度代表性偏差率（P95百分位数）

    输入: entity_deviations = {
        "use001": {"name": "用户1", "deviations": [2.3, 5.1, ..., 8.9]},
        ...
    }

    输出: {
        "use001": {"name": "用户1", "p95_deviation": 12.5},
        ...
    }
    """
    import numpy as np

    representative_deviations = {}

    for entity_id, data in entity_deviations.items():
        deviations = data["deviations"]

        if len(deviations) == 0:
            continue  # 无有效数据

        # 计算P95（95百分位数）
        p95 = np.percentile(deviations, 95)

        representative_deviations[entity_id] = {
            "name": data["name"],
            "p95_deviation": round(p95, 1)  # 保留1位小数
        }

    return representative_deviations

def calculate_representative_deviation(entity_deviations):
    """
    为每个主体计算月度代表性偏差率（P95百分位数）

    输入: entity_deviations = {
        "use001": {"name": "用户1", "deviations": [2.3, 5.1, ..., 8.9]},
        ...
    }

    输出: {
        "use001": {"name": "用户1", "p95_deviation": 12.5},
        ...
    }
    """
    import numpy as np

    representative_deviations = {}

    for entity_id, data in entity_deviations.items():
        deviations = data["deviations"]

        if len(deviations) == 0:
            continue  # 无有效数据

        # 计算P95（95百分位数）
        p95 = np.percentile(deviations, 95)

        representative_deviations[entity_id] = {
            "name": data["name"],
            "p95_deviation": round(p95, 1)  # 保留1位小数
        }

    return representative_deviations

def classify_and_limit_risks(representative_deviations,
                             medium_threshold,
                             high_threshold,
                             max_high_count=50,
                             max_medium_count=100):
    """
    按P95偏差率分类主体，并控制输出数量

    输入:
        representative_deviations = {
            "use001": {"name": "用户1", "p95_deviation": 45.8},
            ...
        }
        medium_threshold: 中风险阈值（如8%）
        high_threshold: 高风险阈值（如15%）
        max_high_count: 最多返回多少个高风险主体
        max_medium_count: 最多返回多少个中风险主体

    输出: {
        "medium_risk": {"用户A": 12.3, "用户B": 10.5, ...},
        "high_risk": {"用户C": 45.8, "用户D": 35.2, ...}
    }
    """
    # 按P95偏差率降序排序
    sorted_entities = sorted(
        representative_deviations.items(),
        key=lambda x: x[1]["p95_deviation"],
        reverse=True
    )

    medium_risk = {}
    high_risk = {}

    for entity_id, data in sorted_entities:
        deviation = data["p95_deviation"]
        name = data["name"]

        # 高风险分类
        if deviation >= high_threshold and len(high_risk) < max_high_count:
            high_risk[name] = deviation

        # 中风险分类
        elif (deviation >= medium_threshold and
              deviation < high_threshold and
              len(medium_risk) < max_medium_count):
            medium_risk[name] = deviation

    return {
        "medium_risk": medium_risk,
        "high_risk": high_risk
    }

def convert_to_chinese_month(date_str):
    """
    将日期字符串转换为中文年月格式

    输入: date_str = "2025-10-01" 或 "2025-10"
    输出: "2025年10月"
    """
    # 分割日期字符串
    parts = date_str.split('-')
    year = parts[0]
    month = parts[1]

    # 去除月份中的前导零
    month = month.lstrip('0') if len(month) > 1 else month

    return f"{year}年{month}月"

def count_unique_entities(data):
    """
    统计API响应数据中的唯一主体数量

    输入: data = [
        {"id": "gen001", "name": "发电机1", "tvValue": [...]},
        {"id": "gen002", "name": "发电机2", "tvValue": [...]},
        {"id": "gen001", "name": "发电机1", "tvValue": [...]},  # 重复ID
        ...
    ]
    输出: 唯一主体数量 (int)
    """
    unique_entities = set()
    for item in data:
        unique_entities.add(item["id"])
    return len(unique_entities)

def filter_frequency_anomalies(frequency_data):
    """
    筛选交易频率异常的主体

    输入: frequency_data = [
        {"id": "gen001", "name": "发电机1", "value": 2.5},
        {"id": "gen002", "name": "发电机2", "value": 0.2},
        {"id": "gen003", "name": "发电机3", "value": 1.2},
        ...
    ]

    输出: {
        "ultra_high": {"发电机1": 2.5},    # >2
        "ultra_low": {"发电机2": 0.2}      # <0.3
    }
    """
    ultra_high = {}
    ultra_low = {}

    for item in frequency_data:
        freq = item["value"]
        name = item["name"]

        if freq > 2.0:
            ultra_high[name] = round(freq, 2)
        elif freq < 0.3:
            ultra_low[name] = round(freq, 2)

    # 按频率降序排序
    ultra_high = dict(sorted(ultra_high.items(), key=lambda x: x[1], reverse=True))
    ultra_low = dict(sorted(ultra_low.items(), key=lambda x: x[1], reverse=True))

    return {
        "ultra_high": ultra_high,
        "ultra_low": ultra_low
    }

def process_zsfyjzdt(zsfyjzdt_data, top_units_count, top_days_count):
    """
    处理阻塞费用集中度-天数据

    输入: zsfyjzdt_data = [
        {"timeTitle": "2025-10-01", "id": "gen001", "name": "机组A", "value": 0.35},
        {"timeTitle": "2025-10-01", "id": "gen002", "name": "机组B", "value": 0.28},
        ...
    ]

    输出: {
        "机组E": {
            "avg": 0.39,
            "top_days": [
                {"date": "2025-10-02", "max_value": 0.45},
                {"date": "2025-10-03", "max_value": 0.42}
            ]
        },
        ...
    }
    """
    if not zsfyjzdt_data:
        return {}

    # 按机组分组计算平均值
    unit_data = {}
    for item in zsfyjzdt_data:
        unit_id = item["id"]
        unit_name = item["name"]
        value = item["value"]
        date = item["timeTitle"]

        if unit_id not in unit_data:
            unit_data[unit_id] = {
                "name": unit_name,
                "values": [],
                "daily_max": {}  # 存储每日最大值
            }

        unit_data[unit_id]["values"].append(value)

        # 记录每日最大值
        if date not in unit_data[unit_id]["daily_max"] or value > unit_data[unit_id]["daily_max"][date]:
            unit_data[unit_id]["daily_max"][date] = value

    # 计算每个机组的平均集中度
    unit_averages = {}
    for unit_id, data in unit_data.items():
        avg_value = sum(data["values"]) / len(data["values"])
        unit_averages[unit_id] = {
            "name": data["name"],
            "avg": round(avg_value, 2),
            "daily_max": data["daily_max"]
        }

    # 选择平均集中度最高的前a个机组
    top_units = sorted(unit_averages.items(), key=lambda x: x[1]["avg"], reverse=True)[:top_units_count]

    # 为每个top机组找出集中度最大值出现的前b天
    result = {}
    for unit_id, unit_info in top_units:
        unit_name = unit_info["name"]
        daily_max = unit_info["daily_max"]

        # 按日最大值排序，取前b天
        top_days = sorted(daily_max.items(), key=lambda x: x[1], reverse=True)[:top_days_count]

        result[unit_name] = {
            "avg": unit_info["avg"],
            "top_days": [
                {"date": date, "max_value": value}
                for date, value in top_days
            ]
        }

    return result

def process_zsfyjzdy(zsfyjzdy_data, top_units_count):
    """
    处理阻塞费用集中度-月数据

    输入: zsfyjzdy_data = [
        {"timeTitle": "2025-10", "id": "gen001", "name": "机组A", "value": 0.36},
        {"timeTitle": "2025-10", "id": "gen002", "name": "机组B", "value": 0.38},
        ...
    ]

    输出: {"机组B": 0.38, "机组E": 0.39, "机组A": 0.36}
    """
    if not zsfyjzdy_data:
        return {}

    # 按集中度降序排序，取前10%（约34个机组）
    total_units = len(zsfyjzdy_data)
    top_10_percent_count = max(1, int(total_units * 0.1))

    # 先取前10%
    sorted_data = sorted(zsfyjzdy_data, key=lambda x: x["value"], reverse=True)[:top_10_percent_count]

    # 从前10%中选出集中度最高的前a个机组
    top_units = sorted_data[:top_units_count]

    # 构造字典格式
    result = {}
    for item in top_units:
        result[item["name"]] = item["value"]

    return result

def process_ssrqjglszszt(ssrqjglszszt_data, top_days_count):
    """
    处理实时-日前价格离散指数-总-天数据

    输入: ssrqjglszszt_data = [
        {"timeTitle": "2025-10-01", "value": 12.3},
        {"timeTitle": "2025-10-02", "value": 15.5},
        ...
    ]

    输出: {
        "2025-10-02": 15.5,
        "2025-10-01": 12.3
    }
    """
    if not ssrqjglszszt_data:
        return {}

    # 按离散指数降序排序，取前c天
    sorted_data = sorted(ssrqjglszszt_data, key=lambda x: x["value"], reverse=True)[:top_days_count]

    # 构造字典格式
    result = {}
    for item in sorted_data:
        result[item["timeTitle"]] = item["value"]

    return result

def process_ssrqjglszsdqt(ssrqjglszsdqt_data):
    """
    处理实时-日前价格离散指数-地区-天数据

    输入: ssrqjglszsdqt_data = [
        {"areaId": "area001", "areaName": "区域1", "timeTitle": "2025-10-01", "value": 10.2},
        {"areaId": "area001", "areaName": "区域1", "timeTitle": "2025-10-02", "value": 12.3},
        {"areaId": "area002", "areaName": "区域2", "timeTitle": "2025-10-01", "value": 8.5},
        ...
    ]

    输出: {
        "区域1": {"2025-10-02": 12.3},
        "区域2": {"2025-10-01": 8.5}
    }
    """
    if not ssrqjglszsdqt_data:
        return {}

    # 按地区分组
    area_data = {}
    for item in ssrqjglszsdqt_data:
        area_name = item["areaName"]
        date = item["timeTitle"]
        value = item["value"]

        if area_name not in area_data:
            area_data[area_name] = []

        area_data[area_name].append({"date": date, "value": value})

    # 为每个地区选取该月内离散指数最大值的日期及其值
    result = {}
    for area_name, data_list in area_data.items():
        # 找到最大值对应的日期和值
        max_item = max(data_list, key=lambda x: x["value"])
        result[area_name] = {max_item["date"]: max_item["value"]}

    return result

def convert_numpy_types(obj):
    """
    递归地将所有numpy类型转换为Python原生类型
    """
    import numpy as np

    if isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj

def process_zsfyjzdt_new(zsfyjzdt_data, top_units_count, top_days_count):
    """
    处理阻塞费用集中度-天数据（新格式）
    """
    if not zsfyjzdt_data:
        return {
            "_comment_zsfyjzdt": "阻塞费用集中度-天 筛选平均集中度最高的前 a 个机组,记录集中度最大值出现的前 b 天日期"
        }

    # 按机组分组计算平均值
    unit_data = {}
    for item in zsfyjzdt_data:
        unit_id = item["id"]
        unit_name = item["name"]
        value = float(item["value"])
        date = item["timeTitle"]

        if unit_id not in unit_data:
            unit_data[unit_id] = {
                "name": unit_name,
                "values": [],
                "daily_max": {}  # 存储每日最大值
            }

        unit_data[unit_id]["values"].append(value)

        # 记录每日最大值
        if date not in unit_data[unit_id]["daily_max"] or value > unit_data[unit_id]["daily_max"][date]:
            unit_data[unit_id]["daily_max"][date] = value

    # 计算每个机组的平均集中度
    unit_averages = {}
    for unit_id, data in unit_data.items():
        avg_value = sum(data["values"]) / len(data["values"])
        unit_averages[unit_id] = {
            "name": data["name"],
            "avg": round(avg_value, 2),
            "daily_max": data["daily_max"]
        }

    # 选择平均集中度最高的前a个机组
    top_units = sorted(unit_averages.items(), key=lambda x: x[1]["avg"], reverse=True)[:top_units_count]

    # 构造结果
    result = {
        "_comment_zsfyjzdt": "阻塞费用集中度-天 筛选平均集中度最高的前 a 个机组,记录集中度最大值出现的前 b 天日期"
    }

    for unit_id, unit_info in top_units:
        unit_name = unit_info["name"]
        daily_max = unit_info["daily_max"]

        # 按日最大值排序，取前b天
        top_days = sorted(daily_max.items(), key=lambda x: x[1], reverse=True)[:top_days_count]

        result[unit_name] = {
            "avg": unit_info["avg"],
            "top_days": [
                {date: float(value)}
                for date, value in top_days
            ]
        }

    return result

def process_zsfyjzdy_new(zsfyjzdy_data, top_units_count):
    """
    处理阻塞费用集中度-月数据（新格式）
    """
    if not zsfyjzdy_data:
        return {
            "_comment_zsfyjzdy": "阻塞费用集中度-月 （筛选集中度最高的前 a 个机组，并构造成 {机组名称: 集中度}的字典格式传给大模型，以节约 token）",
            "timeTitle": "",
            "top_units": {}
        }

    # 按集中度降序排序，取前10%（约34个机组）
    total_units = len(zsfyjzdy_data)
    top_10_percent_count = max(1, int(total_units * 0.1))

    # 先取前10%
    sorted_data = sorted(zsfyjzdy_data, key=lambda x: x["value"], reverse=True)[:top_10_percent_count]

    # 从前10%中选出集中度最高的前a个机组
    top_units = sorted_data[:top_units_count]

    # 构造结果
    result = {
        "_comment_zsfyjzdy": "阻塞费用集中度-月 （筛选集中度最高的前 a 个机组，并构造成 {机组名称: 集中度}的字典格式传给大模型，以节约 token）",
        "timeTitle": top_units[0]["timeTitle"] if top_units else "",
        "top_units": {}
    }

    for item in top_units:
        result["top_units"][item["name"]] = float(item["value"])

    return result

def process_ssrqjglszszt_new(ssrqjglszszt_data, top_days_count):
    """
    处理实时-日前价格离散指数-总-天数据（新格式）
    """
    if not ssrqjglszszt_data:
        return {
            "_comment_ssrqjglszszt": "实时-日前价格离散指数-总-天（取离散指数 最大值对应的前 a 天，仅传这些日期及对应指数给大模型）",
            "timeTitle": "",
            "top_days": []
        }

    # 按离散指数降序排序，取前c天
    sorted_data = sorted(ssrqjglszszt_data, key=lambda x: x["value"], reverse=True)[:top_days_count]

    # 构造结果
    result = {
        "_comment_ssrqjglszszt": "实时-日前价格离散指数-总-天（取离散指数 最大值对应的前 a 天，仅传这些日期及对应指数给大模型）",
        "timeTitle": sorted_data[0]["timeTitle"].split('-')[0] + '-' + sorted_data[0]["timeTitle"].split('-')[1] if sorted_data else "",
        "top_days": []
    }

    for item in sorted_data:
        result["top_days"].append({
            "date": item["timeTitle"],
            "value": float(item["value"])
        })

    return result

def process_ssrqjglszsdqt_new(ssrqjglszsdqt_data):
    """
    处理实时-日前价格离散指数-地区-天数据（新格式）
    """
    if not ssrqjglszsdqt_data:
        return {
            "_comment_ssrqjglszsdqt": "实时-日前价格离散指数-地区-天（按地区分组，选取每个地区 该月内离散指数最大值的日期 及其值。）"
        }

    # 按地区分组
    area_data = {}
    for item in ssrqjglszsdqt_data:
        area_name = item["areaName"]
        date = item["timeTitle"]
        value = float(item["value"])

        if area_name not in area_data:
            area_data[area_name] = []

        area_data[area_name].append({"date": date, "value": value})

    # 构造结果
    result = {
        "_comment_ssrqjglszsdqt": "实时-日前价格离散指数-地区-天（按地区分组，选取每个地区 该月内离散指数最大值的日期 及其值。）"
    }

    # 为每个地区选取该月内离散指数最大值的日期及其值
    for area_name, data_list in area_data.items():
        # 找到最大值对应的日期和值
        max_item = max(data_list, key=lambda x: x["value"])
        result[area_name] = {
            "top_days": [
                {
                    "date": max_item["date"],
                    "value": max_item["value"]
                }
            ]
        }

    return result

def process_ssrqjglszsdqy_new(ssrqjglszsdqy_data):
    """
    处理实时-日前价格离散指数-区域-月数据（新格式）
    """
    if not ssrqjglszsdqy_data:
        return {
            "_comment_ssrqjglszsdqy": "实时-日前价格离散指数-区域-月（取离散指数最大的 b 个区域及其离散指数传递给大模型）",
            "timeTitle": "",
            "top_areas": {}
        }

    # 按离散指数降序排序
    sorted_data = sorted(ssrqjglszsdqy_data, key=lambda x: x["value"], reverse=True)

    # 构造结果
    result = {
        "_comment_ssrqjglszsdqy": "实时-日前价格离散指数-区域-月（取离散指数最大的 b 个区域及其离散指数传递给大模型）",
        "timeTitle": sorted_data[0]["timeTitle"] if sorted_data else "",
        "top_areas": {}
    }

    for item in sorted_data:
        result["top_areas"][item["areaName"]] = float(item["value"])

    return result