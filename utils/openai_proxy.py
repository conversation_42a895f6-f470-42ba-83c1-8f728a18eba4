"""
OpenAI兼容代理服务器

功能：
1. 接收标准OpenAI API格式的请求
2. 将请求转发到自定义大模型服务
3. 返回OpenAI兼容格式的响应
4. 支持流式和非流式两种响应模式

典型使用场景：
- 将非OpenAI模型包装成OpenAI兼容服务
- 在企业内部部署模型服务时提供标准API接口
- 开发测试时模拟OpenAI API行为
"""

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, AsyncGenerator, Union
import httpx
import json
from datetime import datetime
import logging
import os
import dotenv

from llm_model_config import OpenAIConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="OpenAI Compatible Proxy")
dotenv.load_dotenv(".env")
# ==================== 配置区域 ====================
# 你的大模型服务配置
openai_config = OpenAIConfig()
LLM_API_URL = openai_config.api_url  # 从环境变量获取大模型服务地址
logger.info(f"LLM_API_URL: {LLM_API_URL}")
logger.info(f"Environment LLM_API_URL: {os.getenv('LLM_API_URL')}")
logger.info(f"OpenAI config api_url: {openai_config.api_url}")

# ==================== OpenAI 格式定义 ====================
class Message(BaseModel):
    """
    OpenAI消息格式定义

    属性：
    - role: 消息角色，可选值：system|user|assistant
    - content: 消息文本内容
    - name: 可选的消息发送者名称
    """
    role: str
    content: str
    name: Optional[str] = None


class ChatCompletionRequest(BaseModel):
    """
    OpenAI聊天完成请求格式

    属性：
    - model: 要使用的模型名称
    - messages: 对话消息列表
    - temperature: 控制生成随机性的参数(0-2)
    - top_p: 核心采样参数(0-1)
    - n: 生成多个结果的数量
    - stream: 是否使用流式响应
    - stop: 停止生成的标记词列表
    - max_tokens: 生成的最大token数
    - presence_penalty: 控制主题重复度(-2.0到2.0)
    - frequency_penalty: 控制用词重复度(-2.0到2.0)
    - user: 用户标识符(用于监控和滥用检测)
    """
    model: str
    messages: List[Message]
    temperature: Optional[float] = 0.7
    top_p: Optional[float] = 1.0
    n: Optional[int] = 1
    stream: Optional[bool] = False
    stop: Optional[List[str]] = None
    max_tokens: Optional[int] = None
    presence_penalty: Optional[float] = 0
    frequency_penalty: Optional[float] = 0
    user: Optional[str] = None


class Choice(BaseModel):
    index: int
    message: Message
    finish_reason: Optional[str] = None


class Usage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Choice]
    usage: Optional[Usage] = None


# ==================== 请求转换器 ==================== 将OpenAI格式 → 大模型格式

class RequestTransformer:
    """
    OpenAI请求格式转换器

    功能：将OpenAI API请求格式转换为自定义大模型所需的格式
    注意：需要根据实际大模型的API规范调整转换逻辑
    """

    @staticmethod
    def transform_request(openai_request: ChatCompletionRequest) -> Dict[str, Any]:
        """
        转换OpenAI格式请求为大模型格式

        参数:
            openai_request: OpenAI格式的请求对象

        返回:
            转换后的字典格式请求，包含以下字段：
            - model: 模型名称
            - messages: 消息列表(已简化格式)
            - temperature: 温度参数
            - top_p: 核心采样参数
            - max_tokens: 最大生成token数
            - stream: 是否流式输出

        实现说明：
        1. 移除了OpenAI特有的参数如presence_penalty
        2. 简化了messages结构
        3. 自动过滤None值字段
        """
        # 示例转换 - 根据实际情况修改
        transformed = {
            "model": openai_request.model,
            "messages": [
                {
                    "role": msg.role,
                    "content": msg.content
                }
                for msg in openai_request.messages
            ],
            "temperature": openai_request.temperature,
            "top_p": openai_request.top_p,
            "max_tokens": openai_request.max_tokens,
            "stream": openai_request.stream
        }

        # 移除None值
        return {k: v for k, v in transformed.items() if v is not None}


# ==================== 响应转换器 ====================
class ResponseTransformer:
    """将大模型响应转换为OpenAI格式"""

    @staticmethod
    def transform_response(llm_response: Dict[str, Any], model: str) -> ChatCompletionResponse:
        """
        将大模型响应转换为OpenAI格式
        这里需要根据你的大模型响应格式进行调整
        """
        # 示例转换 - 根据实际情况修改
        return ChatCompletionResponse(
            id=llm_response.get("id"),
            created=llm_response.get("created", int(datetime.now().timestamp())),
            model=model,
            choices=[
                Choice(
                    index=llm_response.get("choices", [])[0].get("index", 0),
                    message=Message(
                        role=llm_response.get("choices", [])[0].get("message", {}).get("role", ""),
                        content=llm_response.get("choices", [])[0].get("message", {}).get("content", "")
                    ),
                    finish_reason=llm_response.get("choices", [])[0].get("finish_reason", "stop")
                )
            ],
            usage=Usage(
                prompt_tokens=llm_response.get("usage", {}).get("prompt_tokens", 0),
                completion_tokens=llm_response.get("usage", {}).get("completion_tokens", 0),
                total_tokens=llm_response.get("usage", {}).get("total_tokens", 0)
            )
        )

    @staticmethod
    def transform_stream_chunk(chunk: str, model: str) -> str:
        """
        将流式响应chunk转换为OpenAI SSE格式
        """
        try:
            # 跳过event:行，只处理data:行
            if chunk.strip().startswith("event:"):
                return ""

            # 处理data:行
            if chunk.strip().startswith("data:"):
                chunk = chunk.replace("data:", "").strip()

            if chunk == "[DONE]":
                return "data: [DONE]\n\n"

            # 跳过空行
            if not chunk.strip():
                return ""

            chunk_data = json.loads(chunk)

            # 提取choices数据
            choices = chunk_data.get("choices", [])
            if not choices:
                return ""

            choice = choices[0]
            delta = choice.get("delta", {})

            # 构建OpenAI格式的delta
            openai_delta = {}
            if "role" in delta:
                openai_delta["role"] = delta["role"]
            if "content" in delta:
                openai_delta["content"] = delta["content"]

            # 转换为OpenAI格式
            openai_chunk = {
                "id": chunk_data.get("id", None),
                "object": chunk_data.get("object", "chat.completion.chunk"),
                "created": chunk_data.get("created", int(datetime.now().timestamp())),
                "model": model,
                "choices": [
                    {
                        "index": choice.get("index", 0),
                        "delta": openai_delta,
                        "finish_reason": choice.get("finish_reason", None)
                    }
                ]
            }

            return f"data: {json.dumps(openai_chunk)}\n\n"
        except Exception as e:
            logger.error(f"Error transforming stream chunk: {e}")
            return ""


# ==================== 代理服务 ====================
class ProxyService:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=60.0)

    async def forward_request(self, request: ChatCompletionRequest, api_key: str) -> Dict[str, Any]:
        """转发非流式请求"""
        # 转换请求格式
        transformed_request = RequestTransformer.transform_request(request)

        # 发送请求到大模型
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json;charset=utf-8"
        }

        try:
            response = await self.client.post(
                LLM_API_URL,
                json=transformed_request,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            logger.error(f"Error forwarding request: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def forward_stream_request(self, request: ChatCompletionRequest, api_key: str) -> AsyncGenerator[str, None]:
        """转发流式请求"""
        # 转换请求格式
        transformed_request = RequestTransformer.transform_request(request)

        # 发送流式请求到大模型
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json;charset=utf-8",
            "Accept": "text/event-stream;charset=utf-8"
        }

        try:
            async with self.client.stream(
                    "POST",
                    LLM_API_URL,
                    json=transformed_request,
                    headers=headers
            ) as response:
                response.raise_for_status()

                # 发送初始chunk
                initial_chunk = {
                    "id": None,
                    "object": "chat.completion.chunk",
                    "created": int(datetime.now().timestamp()),
                    "model": request.model,
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"role": "assistant"},
                            "finish_reason": None
                        }
                    ]
                }
                yield f"data: {json.dumps(initial_chunk)}\n\n"

                # 转发流式响应
                async for line in response.aiter_lines():
                    if line:
                        transformed_chunk = ResponseTransformer.transform_stream_chunk(line, request.model)
                        if transformed_chunk:
                            yield transformed_chunk

                # 发送结束标记
                yield "data: [DONE]\n\n"

        except httpx.HTTPStatusError as e:
            error_body = await e.response.aread()
            #logger.error(f"Error in stream forwarding: {e}")
            logger.error(f"Error in error_body:{error_body}")
            error_chunk = {
                "error": {
                    "message": str(e),
                    "type": "proxy_error",
                    "code": 500
                }
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"


# 创建代理服务实例
proxy_service = ProxyService()


# ==================== 依赖函数 ====================
def get_api_key(request: Request) -> str:
    """从请求头中提取API密钥的依赖函数"""
    auth_header = request.headers.get("authorization", "")
    if auth_header.startswith("Bearer "):
        return auth_header[7:]  # 移除 "Bearer " 前缀
    raise HTTPException(status_code=401, detail="Missing or invalid Authorization header")



#
# @app.post("/v1/chat/completions")
# async def chat_completions(
#         request: ChatCompletionRequest,
#         api_key: str = Depends(get_api_key)
# ):
#     logger.info(f"收到请求: {request}")
#     if request.stream:
#         return StreamingResponse(
#             proxy_service.forward_stream_request(request, api_key),
#             media_type="text/event-stream"
#         )
#     else:
#         llm_response = await proxy_service.forward_request(request, api_key)
#         return ResponseTransformer.transform_response(llm_response, request.model)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy"}


# ==================== 启动服务 ====================
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )
