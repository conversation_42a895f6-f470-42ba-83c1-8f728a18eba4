"""
会话管理工具类
提供会话的创建、获取、更新、删除等功能
"""
import uuid
import logging
from typing import Dict, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)


class SessionManager:
    """会话管理器 - 独立工具类"""
    
    def __init__(self, max_idle_hours: int = 24):
        """
        初始化会话管理器
        
        Args:
            max_idle_hours: 会话最大空闲时间（小时）
        """
        self.sessions: Dict[str, Dict] = {}
        self.max_idle_hours = max_idle_hours
        logger.info(f"会话管理器初始化完成，最大空闲时间: {max_idle_hours}小时")
    
    def create_session(self, session_id: str = None) -> str:
        """
        创建新会话
        
        Args:
            session_id: 可选的会话ID，如果不提供则自动生成
            
        Returns:
            str: 会话ID
        """
        if session_id is None:
            session_id = str(uuid.uuid4())
        
        # 如果会话已存在，返回现有会话
        if session_id in self.sessions:
            logger.info(f"会话已存在: {session_id}")
            return session_id
        
        self.sessions[session_id] = {
            "created_at": datetime.now(),
            "last_activity": datetime.now(),
            "message_count": 0
        }
        logger.info(f"创建新会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """
        获取会话信息
        
        Args:
            session_id: 会话ID
            
        Returns:
            Optional[Dict]: 会话信息字典，如果不存在则返回None
        """
        return self.sessions.get(session_id)
    
    def update_session_activity(self, session_id: str) -> bool:
        """
        更新会话活动时间
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 更新是否成功
        """
        if session_id in self.sessions:
            self.sessions[session_id]["last_activity"] = datetime.now()
            self.sessions[session_id]["message_count"] += 1
            return True
        return False
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 删除是否成功
        """
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"删除会话: {session_id}")
            return True
        return False
    
    def cleanup_expired_sessions(self) -> int:
        """
        清理过期的会话
        
        Returns:
            int: 清理的会话数量
        """
        now = datetime.now()
        expired_sessions = []
        
        for session_id, info in self.sessions.items():
            idle_time = now - info["last_activity"]
            if idle_time.total_seconds() > self.max_idle_hours * 3600:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.delete_session(session_id)
            logger.info(f"清理过期会话: {session_id}")
        
        return len(expired_sessions)
    
    def list_sessions(self) -> List[Dict]:
        """
        列出所有会话
        
        Returns:
            List[Dict]: 会话信息列表
        """
        return [
            {
                "session_id": sid,
                "created_at": info["created_at"].isoformat(),
                "last_activity": info["last_activity"].isoformat(),
                "message_count": info["message_count"]
            }
            for sid, info in self.sessions.items()
        ]
    
    def get_session_count(self) -> int:
        """
        获取当前活跃会话数量
        
        Returns:
            int: 活跃会话数量
        """
        return len(self.sessions)
    
    def is_session_valid(self, session_id: str) -> bool:
        """
        检查会话是否有效（存在且未过期）
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 会话是否有效
        """
        if session_id not in self.sessions:
            return False
        
        session = self.sessions[session_id]
        now = datetime.now()
        idle_time = now - session["last_activity"]
        
        return idle_time.total_seconds() <= self.max_idle_hours * 3600
    
    def get_session_stats(self) -> Dict:
        """
        获取会话统计信息
        
        Returns:
            Dict: 统计信息
        """
        total_sessions = len(self.sessions)
        total_messages = sum(info["message_count"] for info in self.sessions.values())
        
        return {
            "total_sessions": total_sessions,
            "total_messages": total_messages,
            "max_idle_hours": self.max_idle_hours,
            "oldest_session": min(
                (info["created_at"] for info in self.sessions.values()),
                default=None
            ),
            "newest_session": max(
                (info["created_at"] for info in self.sessions.values()),
                default=None
            )
        }
