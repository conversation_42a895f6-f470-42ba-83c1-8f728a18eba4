"""
流式对话机器人工具类
提供流式对话、普通对话、历史记录等功能
"""
import logging
from typing import List, Dict, Optional, AsyncGenerator, Sequence
import httpx
import json
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, END, StateGraph
from typing import TypedDict, Annotated
import operator

logger = logging.getLogger(__name__)


# 定义状态结构
class ChatState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], operator.add]


class StreamingChatbot:
    """流式对话机器人 - 通过HTTP调用本地/v1/chat/completions接口"""

    def __init__(self, api_key: str, base_url: str = None, model: str = "gpt-3.5-turbo",
                 temperature: float = 0.7, system_prompt: str = None):
        """
        初始化对话机器人

        Args:
            api_key: OpenAI API密钥
            base_url: API基础URL (可选)
            model: 模型名称
            temperature: 温度参数
            system_prompt: 系统提示词
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.temperature = temperature
        self.system_prompt = system_prompt or (
            "你是一个有用的AI助手。请根据用户的问题提供准确、有帮助的回答。"
            "如果用户询问之前的问题，请结合上下文信息进行回答。"
        )

        # 创建状态图用于会话管理
        self.workflow = StateGraph(ChatState)
        self.workflow.add_node("chat", self._chat_node)
        self.workflow.set_entry_point("chat")
        self.workflow.add_edge("chat", END)

        # 使用内存检查点保存对话历史
        self.memory = MemorySaver()
        self.app = self.workflow.compile(checkpointer=self.memory)

        # HTTP客户端
        self.client = httpx.AsyncClient(timeout=60.0)

        # 修正base_url，确保指向内部接口
        if self.base_url == "http://127.0.0.1:8000/v1":
            self.base_url = "http://127.0.0.1:8000/internal/v1"

        logger.info(f"流式对话机器人初始化完成，模型: {model}，基础URL: {self.base_url}")

    def _chat_node(self, state: ChatState):
        """对话节点 - 仅用于状态管理"""
        # 这个方法不再直接调用LLM，只用于状态管理
        return state

    async def stream_chat(self, user_input: str, session_id: str = "default"):
        """
        流式对话 - 通过HTTP调用内部接口

        Args:
            user_input: 用户输入
            session_id: 会话ID，用于区分不同用户

        Yields:
            str: 流式输出的文本片段（逐token）
        """
        try:
            # 配置线程ID用于上下文管理
            config = {"configurable": {"thread_id": session_id}}

            # 获取当前对话历史
            state = self.app.get_state(config)
            messages = list(state.values.get("messages", [])) if state.values else []

            # 添加系统提示词（如果还没有）
            if not messages or not isinstance(messages[0], SystemMessage):
                messages = [SystemMessage(content=self.system_prompt)] + messages

            # 添加新的用户消息
            messages.append(HumanMessage(content=user_input))

            # 转换为OpenAI格式的消息
            openai_messages = []
            for msg in messages:
                if isinstance(msg, SystemMessage):
                    openai_messages.append({"role": "system", "content": msg.content})
                elif isinstance(msg, HumanMessage):
                    openai_messages.append({"role": "user", "content": msg.content})
                elif isinstance(msg, AIMessage):
                    openai_messages.append({"role": "assistant", "content": msg.content})

            # 构建请求数据
            request_data = {
                "model": self.model,
                "messages": openai_messages,
                "temperature": self.temperature,
                "stream": True
            }

            # 调用内部接口
            url = f"{self.base_url}"
            headers = {"Content-Type": "application/json"}

            full_response = ""
            async with self.client.stream(
                "POST", url, json=request_data, headers=headers
            ) as response:
                response.raise_for_status()

                async for line in response.aiter_lines():
                    if not line.strip():
                        continue

                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀

                        if data_str == "[DONE]":
                            break

                        try:
                            data = json.loads(data_str)
                            choices = data.get("choices", [])
                            if choices:
                                delta = choices[0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    full_response += content
                                    yield content
                        except json.JSONDecodeError:
                            continue

            # 手动更新状态（保存到历史记录）
            messages.append(AIMessage(content=full_response))
            self.app.update_state(config, {"messages": messages})

            logger.info(f"流式对话完成，会话: {session_id}, 响应长度: {len(full_response)}")

        except Exception as e:
            logger.error(f"流式对话错误: {e}")
            yield f"抱歉，发生了错误: {str(e)}"

    def chat(self, user_input: str, session_id: str = "default") -> str:
        """
        普通对话（非流式）

        Args:
            user_input: 用户输入
            session_id: 会话ID

        Returns:
            str: AI回复
        """
        try:
            config = {"configurable": {"thread_id": session_id}}

            # 获取当前对话历史
            state = self.app.get_state(config)
            messages = list(state.values.get("messages", [])) if state.values else []

            # 添加系统提示词（如果还没有）
            if not messages or not isinstance(messages[0], SystemMessage):
                messages = [SystemMessage(content=self.system_prompt)] + messages

            # 添加新的用户消息
            messages.append(HumanMessage(content=user_input))

            # 获取AI回复
            response = self.llm.invoke(messages)

            # 更新状态
            messages.append(response)
            self.app.update_state(config, {"messages": messages})

            logger.info(f"普通对话完成，会话: {session_id}")
            return response.content

        except Exception as e:
            logger.error(f"普通对话错误: {e}")
            return f"抱歉，发生了错误: {str(e)}"

    def get_history(self, session_id: str = "default") -> List[Dict]:
        """
        获取对话历史

        Args:
            session_id: 会话ID

        Returns:
            List[Dict]: 消息列表，每个消息包含role和content
        """
        try:
            config = {"configurable": {"thread_id": session_id}}
            state = self.app.get_state(config)

            if state and state.values:
                messages = state.values.get("messages", [])
                return [
                    {
                        "role": "user" if isinstance(msg, HumanMessage) else "assistant",
                        "content": msg.content,
                        "timestamp": getattr(msg, 'timestamp', None)
                    }
                    for msg in messages
                ]
            return []

        except Exception as e:
            logger.error(f"获取历史记录错误: {e}")
            return []

    def clear_history(self, session_id: str = "default") -> bool:
        """
        清除对话历史

        Args:
            session_id: 会话ID

        Returns:
            bool: 清除是否成功
        """
        try:
            config = {"configurable": {"thread_id": session_id}}
            # 重置状态为空
            self.app.update_state(config, {"messages": []})
            logger.info(f"清除对话历史，会话: {session_id}")
            return True

        except Exception as e:
            logger.error(f"清除历史记录错误: {e}")
            return False

    def update_system_prompt(self, new_prompt: str) -> bool:
        """
        更新系统提示词

        Args:
            new_prompt: 新的系统提示词

        Returns:
            bool: 更新是否成功
        """
        try:
            self.system_prompt = new_prompt
            logger.info("系统提示词已更新")
            return True

        except Exception as e:
            logger.error(f"更新系统提示词错误: {e}")
            return False

    def get_model_info(self) -> Dict:
        """
        获取模型信息

        Returns:
            Dict: 模型信息
        """
        return {
            "model": self.model,
            "temperature": self.temperature,
            "base_url": self.base_url,
            "system_prompt": self.system_prompt[:100] + "..." if len(self.system_prompt) > 100 else self.system_prompt
        }

    def test_connection(self) -> bool:
        """
        测试与LLM的连接

        Returns:
            bool: 连接是否正常
        """
        try:
            test_message = [HumanMessage(content="Hello")]
            response = self.llm.invoke(test_message)
            return response is not None

        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
