#!/usr/bin/env python3
"""
测试 /v1/chat/completions 接口
"""

import asyncio
import aiohttp
import json

async def test_openai_api():
    """测试 OpenAI 兼容接口"""
    
    test_data = {
        "model": "deepseek-v3-250324",
        "messages": [
            {"role": "user", "content": "你好，请简单介绍一下自己"}
        ],
        "temperature": 0.7,
        "stream": False
    }
    
    url = "http://localhost:8000/v1/chat/completions"
    
    try:
        async with aiohttp.ClientSession() as session:
            print("发送请求到:", url)
            print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
            print("-" * 50)
            
            async with session.post(
                url,
                json=test_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status != 200:
                    print(f"错误: HTTP {response.status}")
                    print(await response.text())
                    return
                
                result = await response.json()
                print("响应结果:")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_openai_api())
