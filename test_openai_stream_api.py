#!/usr/bin/env python3
"""
测试 /v1/chat/completions 流式接口
"""

import asyncio
import aiohttp
import json

async def test_openai_stream_api():
    """测试 OpenAI 兼容流式接口"""
    
    test_data = {
        "model": "deepseek-v3-250324",
        "messages": [
            {"role": "user", "content": "请用一句话介绍一下新疆"}
        ],
        "temperature": 0.7,
        "stream": True
    }
    
    url = "http://localhost:8000/v1/chat/completions"
    
    try:
        async with aiohttp.ClientSession() as session:
            print("发送流式请求到:", url)
            print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
            print("-" * 50)
            print("开始接收流式响应...")
            print("-" * 50)
            
            async with session.post(
                url,
                json=test_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status != 200:
                    print(f"错误: HTTP {response.status}")
                    print(await response.text())
                    return
                
                token_count = 0
                full_content = ""
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if not line:
                        continue
                    
                    print(f"收到原始数据: {line}")
                    
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除 "data: " 前缀
                        
                        if data_str == "[DONE]":
                            print("流式响应结束")
                            break
                        
                        try:
                            data = json.loads(data_str)
                            choices = data.get("choices", [])
                            if choices:
                                delta = choices[0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    token_count += 1
                                    full_content += content
                                    print(f"Token {token_count}: '{content}'", end="")
                        except json.JSONDecodeError:
                            print(f"无法解析JSON: {data_str}")
                
                print(f"\n\n流式响应结束")
                print(f"总共收到 {token_count} 个token")
                print(f"完整内容: {full_content}")
                
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_openai_stream_api())
