#!/usr/bin/env python3
"""
测试通过openai_proxy的流式分析接口
"""

import asyncio
import aiohttp
import json
import sys

async def test_stream_analysis():
    """测试 /api/chat/stream/analysis 接口通过 openai_proxy"""
    
    # 测试数据 - 符合processing.py期望的格式
    test_data = {
        "message": {
            "fdczcqhyjypl": [
                {"id": "gen001", "name": "发电机1", "value": 2.5, "timeTitle": "2024-01-15"},
                {"id": "gen002", "name": "发电机2", "value": 0.2, "timeTitle": "2024-01-15"},
                {"id": "gen003", "name": "发电机3", "value": 1.2, "timeTitle": "2024-01-15"}
            ],
            "ydczcqhyjypl": [
                {"id": "load001", "name": "用户A", "value": 2.8, "timeTitle": "2024-01-15"},
                {"id": "load002", "name": "用户B", "value": 0.25, "timeTitle": "2024-01-15"}
            ],
            "zsbphzjbdl": [
                {"timeTitle": "2024-01", "value": 100.5},
                {"timeTitle": "2024-02", "value": 98.3},
                {"timeTitle": "2024-03", "value": 102.1},
                {"timeTitle": "2024-04", "value": 99.8},
                {"timeTitle": "2024-05", "value": 101.2},
                {"timeTitle": "2024-06", "value": 103.0}
            ],
            "ydczqrqsbdl": [],
            "ydczcqjhydl": [],
            "ydcsjydl": [],
            "fdcrqjydl": [],
            "fdczcqjhydl": [],
            "fdcsjfdl": []
        },
        "session_id": "test_proxy_session_123"
    }
    
    url = "http://localhost:8000/api/chat/stream/analysis"
    
    try:
        async with aiohttp.ClientSession() as session:
            print("发送请求到:", url)
            print("测试数据:", json.dumps(test_data, ensure_ascii=False, indent=2))
            print("-" * 50)
            
            async with session.post(
                url,
                json=test_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status != 200:
                    print(f"错误: HTTP {response.status}")
                    print(await response.text())
                    return
                
                print("开始接收流式响应...")
                print("-" * 50)
                
                full_content = ""
                token_count = 0
                
                # 读取流式响应
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line:
                        print(f"收到原始数据: {line}")
                        
                        # 解析SSE格式
                        if line.startswith("data: "):
                            try:
                                data_content = line[6:]  # 移除 "data: " 前缀
                                if data_content != "[DONE]":
                                    data = json.loads(data_content)
                                    if data.get("type") == "token":
                                        content = data.get('content', '')
                                        full_content += content
                                        token_count += 1
                                        print(f"Token {token_count}: '{content}'", end="", flush=True)
                                    elif data.get("type") == "end":
                                        print(f"\n\n流式响应结束")
                                        print(f"总共收到 {token_count} 个token")
                                        print(f"完整内容: {full_content}")
                                    elif data.get("type") == "error":
                                        print(f"\n错误: {data.get('content', '')}")
                            except json.JSONDecodeError as e:
                                print(f"JSON解析错误: {e}")
                
                print("\n" + "-" * 50)
                print("测试完成")
                
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    print("测试通过openai_proxy的流式分析接口...")
    asyncio.run(test_stream_analysis())
