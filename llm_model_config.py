import os
import logging
import json

logger = logging.getLogger(__name__)

class OpenAIConfig:
    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.api_url = os.getenv("LLM_API_URL")
        self.api_base = os.getenv("OPENAI_API_BASE")
        self.model_name = os.getenv("OPENAI_MODEL_NAME", "deepseek-v3-250324")
        self.max_tokens = int(os.getenv("OPENAI_MAX_TOKENS", "4096"))
        self.temperature = float(os.getenv("OPENAI_TEMPERATURE", "0.7"))
    def to_dict(self):
        return {
            "api_key": self.api_key,
            "api_url": self.api_url,
            "api_base": self.api_base,
            "model_name": self.model_name,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
        }
    
    def __str__(self):
        return json.dumps(self.to_dict(), indent=4)


