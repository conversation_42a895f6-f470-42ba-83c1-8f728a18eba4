# 使用基于稳定版Debian的Python镜像
FROM python:3.9-bullseye

# 在容器内设置一个工作目录，后续的指令都会在这个路径下执行
WORKDIR /app

# 先将依赖文件复制到容器的工作目录
COPY requirements.txt .

# 设置环境变量
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 将当前目录下的所有项目源代码复制到容器的/app目录
COPY . .

# 配置文件外置
RUN mkdir -p /app/config
VOLUME /app/config

# 声明容器运行时需要暴露的端口
EXPOSE 8000

# 定义容器启动后自动执行的命令
CMD ["python", "chat_api.py"]